from typing import Any, List
import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.company import Company
from app.models.user import User
from app.models.system_config import SystemConfig
from app.schemas.company import CompanyCreate, CompanyUpdate, Company as CompanySchema
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 首先定义所有具体的路由（包含路径参数的特定路径）
# 注意：路由定义的顺序很重要，特定路由应该在通用路由之前定义
@router.post("/{company_id}/blacklist", response_model=CompanySchema)
def add_company_to_blacklist(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    将公司加入黑名单
    """
    # 权限检查
    if current_user.level < 4:
        logger.warning(f"用户[{current_user.username}]尝试加入黑名单操作被拒绝，权限不足，用户级别:{current_user.level}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="没有足够的权限执行此操作")

    # 查找公司
    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        logger.error(f"管理员[{current_user.username}]尝试将不存在的公司加入黑名单，公司ID:{company_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="公司不存在")

    # 记录操作开始
    logger.info(f"管理员[{current_user.username}]准备将公司[{company.name}]加入黑名单，公司ID:{company_id}，操作者级别:{current_user.level}")

    # 执行操作
    old_status = company.is_blacklisted
    company.is_blacklisted = True

    try:
        db.add(company)
        db.commit()
        db.refresh(company)

        # 记录操作成功
        logger.info(f"黑名单操作完成，公司[{company.name}]已成功加入黑名单，公司ID:{company_id}，原状态:{old_status}，新状态:{company.is_blacklisted}")
        return company
    except Exception as e:
        db.rollback()
        logger.error(f"将公司[{company.name}]加入黑名单时发生错误，公司ID:{company_id}，错误:{str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="操作失败")

@router.delete("/{company_id}/blacklist", response_model=CompanySchema)
def remove_company_from_blacklist(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    将公司移出黑名单
    """
    # 权限检查
    if current_user.level < 4:
        logger.warning(f"用户[{current_user.username}]尝试移出黑名单操作被拒绝，权限不足，用户级别:{current_user.level}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="没有足够的权限执行此操作")

    # 查找公司
    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        logger.error(f"管理员[{current_user.username}]尝试将不存在的公司移出黑名单，公司ID:{company_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="公司不存在")

    # 记录操作开始
    logger.info(f"管理员[{current_user.username}]准备将公司[{company.name}]移出黑名单，公司ID:{company_id}，操作者级别:{current_user.level}")

    # 执行操作
    old_status = company.is_blacklisted
    company.is_blacklisted = False

    try:
        db.add(company)
        db.commit()
        db.refresh(company)

        # 记录操作成功
        logger.info(f"黑名单移出操作完成，公司[{company.name}]已成功移出黑名单，公司ID:{company_id}，原状态:{old_status}，新状态:{company.is_blacklisted}")
        return company
    except Exception as e:
        db.rollback()
        logger.error(f"将公司[{company.name}]移出黑名单时发生错误，公司ID:{company_id}，错误:{str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="操作失败")

@router.post("/{company_id}/verify", response_model=CompanySchema)
def verify_company(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    审核通过公司
    """
    # 权限检查
    if current_user.level < 4:
        logger.warning(f"用户[{current_user.username}]尝试审核操作被拒绝，权限不足，用户级别:{current_user.level}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="没有足够的权限执行此操作")

    # 查找公司
    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        logger.error(f"管理员[{current_user.username}]尝试审核不存在的公司，公司ID:{company_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="公司不存在")

    # 记录操作开始
    logger.info(f"管理员[{current_user.username}]准备审核通过公司[{company.name}]，公司ID:{company_id}，操作者级别:{current_user.level}")

    # 执行操作
    old_status = company.is_verified
    company.is_verified = True

    try:
        db.add(company)
        db.commit()
        db.refresh(company)

        # 记录操作成功
        logger.info(f"审核操作完成，公司[{company.name}]已成功通过审核，公司ID:{company_id}，原状态:{old_status}，新状态:{company.is_verified}")
        return company
    except Exception as e:
        db.rollback()
        logger.error(f"审核公司[{company.name}]时发生错误，公司ID:{company_id}，错误:{str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="操作失败")

# 审核拒绝API - 使用更具体的路径避免路由冲突
@router.delete("/{company_id}/verification/reject", response_model=CompanySchema)
def reject_company(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    审核拒绝公司
    """
    # 权限检查
    if current_user.level < 4:
        logger.warning(f"用户[{current_user.username}]尝试审核拒绝操作被拒绝，权限不足，用户级别:{current_user.level}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="没有足够的权限执行此操作")

    # 查找公司
    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        logger.error(f"管理员[{current_user.username}]尝试审核拒绝不存在的公司，公司ID:{company_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="公司不存在")

    # 记录操作开始
    logger.info(f"管理员[{current_user.username}]准备审核拒绝公司[{company.name}]，公司ID:{company_id}，操作者级别:{current_user.level}")

    # 执行操作
    old_status = company.is_verified
    company.is_verified = False

    try:
        db.add(company)
        db.commit()
        db.refresh(company)

        # 记录操作成功
        logger.info(f"审核拒绝操作完成，公司[{company.name}]已被拒绝审核，公司ID:{company_id}，原状态:{old_status}，新状态:{company.is_verified}")
        return company
    except Exception as e:
        db.rollback()
        logger.error(f"审核拒绝公司[{company.name}]时发生错误，公司ID:{company_id}，错误:{str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="操作失败")

# 添加一个POST方法的reject路由，以防客户端使用POST而不是DELETE
@router.post("/{company_id}/verification/reject", response_model=CompanySchema)
def reject_company_post(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """审核拒绝公司 (POST方法)"""
    # 直接调用DELETE方法的实现
    return reject_company(company_id, db, current_user)

# 保留原来的reject路径，但使用PUT方法
@router.put("/{company_id}/reject", response_model=CompanySchema)
def reject_company_put(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """审核拒绝公司 (PUT方法)"""
    # 直接调用DELETE方法的实现
    return reject_company(company_id, db, current_user)

# 测试路由
@router.delete("/{company_id}/test-reject")
def test_reject_route(company_id: str):
    """测试路由是否工作"""
    return {"message": f"测试路由工作正常，公司ID: {company_id}"}

@router.get("/test-route-reload")
def test_route_reload():
    """测试路由重新加载"""
    return {"message": "路由重新加载成功！", "timestamp": "2025-05-24-05:05"}

# 供应商状态检查API（游客可访问）
@router.get("/check-status")
def check_supplier_status(
    company_name: str,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    检查供应商状态（游客可访问）
    用于报价页面的供应商状态检查
    """
    try:
        logger.info(f"游客检查供应商状态，公司名称: {company_name}")

        # 查找匹配的供应商（模糊匹配）
        companies = db.query(Company).filter(
            Company.is_supplier == True,
            Company.name.ilike(f"%{company_name}%")
        ).all()

        if not companies:
            logger.info(f"未找到匹配的供应商，公司名称: {company_name}")
            return {
                "found": False,
                "status": "new",
                "message": "系统中未找到该供应商信息，将作为新供应商处理。",
                "can_proceed": True
            }

        # 取第一个匹配的供应商
        company = companies[0]
        logger.info(f"找到匹配的供应商，公司名称: {company.name}，黑名单状态: {company.is_blacklisted}，审核状态: {company.is_verified}")

        # 检查黑名单状态
        if company.is_blacklisted:
            return {
                "found": True,
                "status": "blacklisted",
                "message": "该供应商已被加入黑名单，无法提交报价。如有疑问，请联系管理员。",
                "can_proceed": False,
                "company_name": company.name
            }

        # 检查审核状态
        if not company.is_verified:
            return {
                "found": True,
                "status": "pending",
                "message": "该供应商尚未通过审核，暂时无法提交报价。请等待管理员审核通过后再试。",
                "can_proceed": False,
                "company_name": company.name
            }

        # 供应商状态正常
        return {
            "found": True,
            "status": "verified",
            "message": "该供应商已通过审核，可以正常提交报价。",
            "can_proceed": True,
            "company_name": company.name
        }

    except Exception as e:
        logger.error(f"检查供应商状态时发生错误，公司名称: {company_name}，错误: {str(e)}")
        return {
            "found": False,
            "status": "error",
            "message": "供应商状态检查服务暂时不可用，您可以继续提交报价。管理员会在审核时检查供应商资质。",
            "can_proceed": True
        }

# 然后定义通用路由（这些路由会匹配更广泛的模式）
@router.get("/", response_model=List[CompanySchema])
def read_companies(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取公司列表
    """
    # 权限检查
    if not deps.check_permission(current_user, "company", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 根据用户级别过滤
    if current_user.level == 5:  # 超级管理员可以查看所有公司
        companies = db.query(Company).offset(skip).limit(limit).all()
    elif current_user.level == 4:  # 总部管理员只能查看自己公司及其关联的供应商
        # 获取当前用户所属公司
        user_company = db.query(Company).filter(Company.id == current_user.company_id).first()
        if user_company:
            # 查询所有供应商
            suppliers = db.query(Company).filter(Company.is_supplier == True).all()
            # 合并结果
            companies = [user_company] + suppliers
        else:
            companies = []
    elif current_user.level == 3:  # 分公司负责人只能查看自己的公司
        companies = db.query(Company).filter(Company.id == current_user.company_id).all()
    else:  # 其他用户只能查看自己的公司
        if current_user.company_id:
            companies = db.query(Company).filter(Company.id == current_user.company_id).all()
        else:
            companies = []

    return companies

@router.get("/{company_id}", response_model=CompanySchema)
def read_company(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取公司详情
    """
    # 权限检查
    if not deps.check_permission(current_user, "company", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在",
        )

    # 权限检查：只有超级管理员可以查看任何公司，其他用户只能查看自己的公司或供应商
    if current_user.level < 5 and company.id != current_user.company_id and not company.is_supplier:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    return company

@router.post("/", response_model=CompanySchema)
def create_company(
    *,
    db: Session = Depends(deps.get_db),
    company_in: CompanyCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新公司

    is_verified字段自动设置逻辑：
    - 供应商(is_supplier=True)：根据系统配置supplier_need_verify决定
      - supplier_need_verify=true时，is_verified=false (需要审核)
      - supplier_need_verify=false时，is_verified=true (自动通过)
    - 普通公司(is_supplier=False)：始终设为is_verified=true
    - 配置不存在时，默认不需要审核
    """
    # 权限检查
    if not deps.check_permission(current_user, "company", "create", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 只有超级管理员和总部管理员可以创建公司
    if current_user.level < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 查询系统配置，决定供应商注册是否需要审核
    config = db.query(SystemConfig).filter(SystemConfig.key == 'supplier_need_verify').first()
    need_verify = config.value.get('enabled', False) if config else False

    # 准备公司数据
    company_data = company_in.dict()

    # 根据配置设置供应商的is_verified初始值
    if company_data.get('is_supplier', False):
        # 对于供应商，根据配置决定是否需要审核
        company_data['is_verified'] = not need_verify
        logger.info(f"创建供应商[{company_data.get('name', 'N/A')}]，审核配置:{need_verify}，初始审核状态:{company_data['is_verified']}，操作者:[{current_user.username}]")
    else:
        # 普通公司始终设为已审核
        company_data['is_verified'] = True
        logger.info(f"创建普通公司[{company_data.get('name', 'N/A')}]，自动通过审核，操作者:[{current_user.username}]")

    # 创建新公司
    company = Company(**company_data)
    db.add(company)
    db.commit()
    db.refresh(company)

    logger.info(f"公司创建成功：ID={company.id}，名称={company.name}，类型={'供应商' if company.is_supplier else '普通公司'}，审核状态={company.is_verified}")

    return company

@router.put("/{company_id}", response_model=CompanySchema)
def update_company(
    *,
    db: Session = Depends(deps.get_db),
    company_id: uuid.UUID,
    company_in: CompanyUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新公司信息
    """
    # 权限检查
    if not deps.check_permission(current_user, "company", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在",
        )

    # 权限检查：只有超级管理员可以更新任何公司，总部管理员只能更新自己的公司或供应商
    if current_user.level < 5 and company.id != current_user.company_id and not company.is_supplier:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 更新公司信息
    update_data = company_in.dict(exclude_unset=True)
    for field in update_data:
        setattr(company, field, update_data[field])

    db.add(company)
    db.commit()
    db.refresh(company)
    return company

@router.delete("/{company_id}", response_model=dict)
def delete_company(
    *,
    db: Session = Depends(deps.get_db),
    company_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除公司（支持级联删除）
    
    对于供应商：
    - 删除所有关联的用户账户
    - 删除所有历史报价记录
    - 删除公司记录
    
    权限要求：
    - 超级管理员可以删除任何公司
    - 总部管理员可以删除供应商
    """
    # 权限检查
    if not deps.check_permission(current_user, "company", "delete", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    company = db.query(Company).filter(Company.id == company_id).first()
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在",
        )

    # 权限检查：超级管理员可以删除任何公司，总部管理员只能删除供应商
    if current_user.level < 5:
        if current_user.level < 4:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有足够的权限执行此操作",
            )
        if not company.is_supplier:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="总部管理员只能删除供应商",
            )

    # 记录删除操作日志
    logger.info(f"开始删除公司：ID={company.id}，名称={company.name}，类型={'供应商' if company.is_supplier else '普通公司'}，操作者:[{current_user.username}]")

    deleted_data = {
        "company_id": str(company.id),
        "company_name": company.name,
        "is_supplier": company.is_supplier,
        "deleted_users": 0,
        "deleted_quotes": 0,
        "deleted_tasks": 0
    }

    try:
        # 1. 删除关联的报价记录
        from app.models.quote import Quote
        quotes = db.query(Quote).filter(Quote.supplier_id == company.id).all()
        deleted_data["deleted_quotes"] = len(quotes)
        for quote in quotes:
            db.delete(quote)
        logger.info(f"删除了 {len(quotes)} 条报价记录")

        # 2. 删除关联的任务（如果公司是任务发布方）
        from app.models.task import Task
        tasks = db.query(Task).filter(Task.company_id == company.id).all()
        deleted_data["deleted_tasks"] = len(tasks)
        for task in tasks:
            # 删除任务相关的报价
            task_quotes = db.query(Quote).filter(Quote.task_id == task.id).all()
            for task_quote in task_quotes:
                db.delete(task_quote)
            db.delete(task)
        logger.info(f"删除了 {len(tasks)} 个任务及其相关报价")

        # 3. 删除关联的用户账户
        from app.models.user import User
        users = db.query(User).filter(User.company_id == company.id).all()
        deleted_data["deleted_users"] = len(users)
        for user in users:
            db.delete(user)
        logger.info(f"删除了 {len(users)} 个用户账户")

        # 4. 最后删除公司记录
        db.delete(company)
        
        # 提交所有删除操作
        db.commit()
        
        logger.info(f"公司删除成功：{deleted_data}")
        
        return {
            "message": "公司删除成功",
            "deleted_data": deleted_data
        }

    except Exception as e:
        db.rollback()
        logger.error(f"删除公司失败：ID={company.id}，错误={str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除失败：{str(e)}",
        )
