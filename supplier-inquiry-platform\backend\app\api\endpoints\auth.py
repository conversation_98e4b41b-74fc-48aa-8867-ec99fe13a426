from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api import deps
from app.core.config import settings
from app.core.security import create_access_token, get_password_hash, verify_password
from app.models.user import User
from app.models.company import Company
from app.models.system_config import SystemConfig
from app.schemas.token import Token
from app.schemas.user import UserCreate, User as UserSchema
from app.schemas.company import CompanyCreate
from app.core.logging import get_logger
from pydantic import BaseModel, EmailStr
from typing import Optional

router = APIRouter()
logger = get_logger(__name__)

# 供应商注册请求模型
class SupplierRegisterRequest(BaseModel):
    # 用户信息
    username: str
    email: EmailStr
    password: str
    name: Optional[str] = None
    phone: Optional[str] = None

    # 公司信息
    company_name: str
    company_address: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None

@router.post("/login", response_model=Token)
def login_access_token(
    request: Request,
    db: Session = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 兼容的token登录，获取访问token
    """
    logger.info(f"用户尝试登录: {form_data.username}")

    user = db.query(User).filter(User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        logger.warning(f"登录失败: 用户名或密码错误 - 用户名: {form_data.username}, IP: {request.client.host}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        logger.warning(f"登录失败: 用户已被禁用 - 用户名: {form_data.username}, IP: {request.client.host}")
        raise HTTPException(status_code=400, detail="用户已被禁用")

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token = create_access_token(user.id, expires_delta=access_token_expires)

    logger.info(f"用户登录成功: {user.username}, ID: {user.id}, IP: {request.client.host}")

    return {
        "access_token": token,
        "token_type": "bearer",
    }

@router.post("/register", response_model=UserSchema)
def register_user(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
) -> Any:
    """
    注册新用户

    根据系统配置决定用户初始状态：
    - 如果启用了用户审核配置，新用户is_active=False（需要审核）
    - 如果未启用审核配置，新用户is_active=True（立即可用）
    """
    logger.info(f"用户注册请求: {user_in.username}, 邮箱: {user_in.email}")

    # 检查用户名是否已存在
    user = db.query(User).filter(User.username == user_in.username).first()
    if user:
        logger.warning(f"注册失败: 用户名已存在 - {user_in.username}, IP: {request.client.host}")
        raise HTTPException(
            status_code=400,
            detail="用户名已存在",
        )
    # 检查邮箱是否已存在
    user = db.query(User).filter(User.email == user_in.email).first()
    if user:
        logger.warning(f"注册失败: 邮箱已存在 - {user_in.email}, IP: {request.client.host}")
        raise HTTPException(
            status_code=400,
            detail="邮箱已存在",
        )

    # 查询系统配置，决定用户注册是否需要审核
    config = db.query(SystemConfig).filter(SystemConfig.key == 'user_need_verify').first()
    need_verify = config.value.get('enabled', False) if config else False

    # 根据配置设置用户的初始激活状态
    initial_is_active = not need_verify  # 如果需要审核，则初始状态为非激活

    logger.info(f"用户注册审核配置: need_verify={need_verify}, initial_is_active={initial_is_active}")

    # 创建新用户
    user = User(
        username=user_in.username,
        email=user_in.email,
        hashed_password=get_password_hash(user_in.password),
        name=user_in.name,
        phone=user_in.phone,
        company_id=user_in.company_id,
        parent_id=user_in.parent_id,
        level=user_in.level,
        is_active=initial_is_active,  # 使用配置决定的初始状态
    )
    db.add(user)
    db.commit()
    db.refresh(user)

    status_msg = "需要审核" if need_verify else "立即可用"
    logger.info(f"用户注册成功: {user.username}, ID: {user.id}, 状态: {status_msg}, IP: {request.client.host}")

    return user

@router.post("/register-supplier", response_model=dict)
async def register_supplier(
    request: Request,
    *,
    db: Session = Depends(deps.get_db),
    supplier_in: SupplierRegisterRequest,
) -> Any:
    """
    供应商注册接口

    同时创建用户和供应商公司，根据系统配置决定审核状态：
    - user_need_verify: 控制用户是否需要审核
    - supplier_need_verify: 控制供应商公司是否需要审核
    """
    # 记录请求详情
    logger.info(f"供应商注册请求: 用户={supplier_in.username}, 公司={supplier_in.company_name}, IP: {request.client.host}")
    logger.debug(f"供应商注册详细数据: {supplier_in.model_dump()}")
    
    # 添加详细的字段验证日志
    logger.info(f"供应商注册字段验证 - username: {supplier_in.username}, email: {supplier_in.email}, name: {supplier_in.name}, phone: {supplier_in.phone}")
    logger.info(f"供应商注册字段验证 - company_name: {supplier_in.company_name}, company_address: {supplier_in.company_address}")
    logger.info(f"供应商注册字段验证 - contact_person: {supplier_in.contact_person}, contact_phone: {supplier_in.contact_phone}, contact_email: {supplier_in.contact_email}")

    # 检查用户名是否已存在
    existing_user = db.query(User).filter(User.username == supplier_in.username).first()
    if existing_user:
        logger.warning(f"供应商注册失败: 用户名已存在 - {supplier_in.username}")
        raise HTTPException(
            status_code=400,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    existing_email = db.query(User).filter(User.email == supplier_in.email).first()
    if existing_email:
        logger.warning(f"供应商注册失败: 邮箱已存在 - {supplier_in.email}")
        raise HTTPException(
            status_code=400,
            detail="邮箱已存在",
        )

    # 检查公司名称是否已存在
    existing_company = db.query(Company).filter(Company.name == supplier_in.company_name).first()
    if existing_company:
        logger.warning(f"供应商注册失败: 公司名称已存在 - {supplier_in.company_name}")
        raise HTTPException(
            status_code=400,
            detail="公司名称已存在",
        )

    # 查询系统配置
    user_config = db.query(SystemConfig).filter(SystemConfig.key == 'user_need_verify').first()
    supplier_config = db.query(SystemConfig).filter(SystemConfig.key == 'supplier_need_verify').first()

    user_need_verify = user_config.value.get('enabled', False) if user_config else False
    supplier_need_verify = supplier_config.value.get('enabled', False) if supplier_config else False

    logger.info(f"审核配置: user_need_verify={user_need_verify}, supplier_need_verify={supplier_need_verify}")

    try:
        # 1. 先创建供应商公司
        company = Company(
            name=supplier_in.company_name,
            address=supplier_in.company_address,
            contact_person=supplier_in.contact_person or supplier_in.name,
            contact_phone=supplier_in.contact_phone or supplier_in.phone,
            contact_email=supplier_in.contact_email or supplier_in.email,
            is_supplier=True,
            is_verified=not supplier_need_verify,  # 根据配置决定是否需要审核
            is_blacklisted=False,
        )
        db.add(company)
        db.flush()  # 获取company.id但不提交事务

        # 2. 创建用户并关联到公司
        user = User(
            username=supplier_in.username,
            email=supplier_in.email,
            hashed_password=get_password_hash(supplier_in.password),
            name=supplier_in.name,
            phone=supplier_in.phone,
            company_id=company.id,
            level=1,  # 普通用户级别
            is_active=not user_need_verify,  # 根据配置决定是否需要审核
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        db.refresh(company)

        # 记录成功日志
        user_status = "需要审核" if user_need_verify else "立即可用"
        company_status = "需要审核" if supplier_need_verify else "已审核"
        logger.info(f"供应商注册成功: 用户={user.username}({user_status}), 公司={company.name}({company_status})")

        return {
            "message": "供应商注册成功",
            "user": {
                "id": str(user.id),
                "username": user.username,
                "email": user.email,
                "name": user.name,
                "is_active": user.is_active,
                "need_user_verify": user_need_verify
            },
            "company": {
                "id": str(company.id),
                "name": company.name,
                "is_verified": company.is_verified,
                "need_supplier_verify": supplier_need_verify
            },
            "next_steps": {
                "can_login": not user_need_verify,
                "can_quote": not supplier_need_verify and not user_need_verify,
                "message": _get_registration_message(user_need_verify, supplier_need_verify)
            }
        }

    except Exception as e:
        db.rollback()
        logger.error(f"供应商注册失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="注册失败，请稍后再试",
        )

def _get_registration_message(user_need_verify: bool, supplier_need_verify: bool) -> str:
    """根据审核配置生成注册成功消息"""
    if user_need_verify and supplier_need_verify:
        return "注册成功！请等待管理员审核用户账户和供应商资质后使用。"
    elif user_need_verify:
        return "注册成功！请等待管理员审核用户账户后使用。"
    elif supplier_need_verify:
        return "注册成功！您可以登录系统，但需要等待管理员审核供应商资质后才能参与报价。"
    else:
        return "注册成功！您可以立即登录并参与报价。"

@router.get("/me", response_model=UserSchema)
def read_users_me(
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户信息
    """
    logger.debug(f"用户请求个人信息: ID: {current_user.id}, 用户名: {current_user.username}")
    return current_user

@router.post("/logout")
def logout(request: Request) -> Any:
    """
    登出

    注意：由于JWT是无状态的，服务器端无法使令牌失效。
    客户端应该删除本地存储的令牌。
    """
    # 获取客户端IP
    client_ip = request.client.host
    logger.info(f"用户登出, IP: {client_ip}")
    return {"detail": "登出成功"}
