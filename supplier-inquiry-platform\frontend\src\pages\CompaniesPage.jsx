import React, { useEffect, useState } from 'react';
import { Layout, Typography, Table, Button, Space, Tag, Card, Input, Select, Modal, message } from 'antd';
import { 
  SearchOutlined, 
  ShopOutlined, 
  UserDeleteOutlined, 
  UserAddOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined 
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import api from '../services/api';

const { Content } = Layout;
const { Title } = Typography;
const { Option } = Select;

const CompaniesPage = () => {
  const { user: currentUser } = useSelector((state) => state.auth);
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [filteredCompanies, setFilteredCompanies] = useState([]);

  // 获取供应商列表
  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const response = await api.get('/companies');
      console.log('获取供应商列表响应:', response);
      setCompanies(response);
      setFilteredCompanies(response);
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      message.error(error.response?.data?.detail || '获取供应商列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  // 搜索和筛选
  useEffect(() => {
    let filtered = companies;

    // 文本搜索
    if (searchText) {
      filtered = filtered.filter(
        company => 
          company.name.toLowerCase().includes(searchText.toLowerCase()) ||
          (company.contact_person && company.contact_person.toLowerCase().includes(searchText.toLowerCase())) ||
          (company.contact_email && company.contact_email.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(company => {
        switch (statusFilter) {
          case 'normal':
            return !company.is_blacklisted && company.is_verified;
          case 'blacklisted':
            return company.is_blacklisted;
          case 'pending':
            return !company.is_verified && !company.is_blacklisted;
          default:
            return true;
        }
      });
    }

    setFilteredCompanies(filtered);
  }, [searchText, statusFilter, companies]);

  // 加入黑名单
  const handleAddToBlacklist = async (companyId, companyName) => {
    Modal.confirm({
      title: '确认加入黑名单',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>确定要将 <strong>{companyName}</strong> 加入黑名单吗？</p>
          <p>加入黑名单后，该供应商将无法提交报价。</p>
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await api.post(`/companies/${companyId}/blacklist`);
          message.success('已成功加入黑名单');
          fetchCompanies();
        } catch (error) {
          message.error(error.response?.data?.detail || '操作失败，请稍后再试');
        }
      }
    });
  };

  // 移出黑名单
  const handleRemoveFromBlacklist = async (companyId, companyName) => {
    Modal.confirm({
      title: '确认移出黑名单',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>确定要将 <strong>{companyName}</strong> 移出黑名单吗？</p>
          <p>移出后，该供应商将可以正常提交报价。</p>
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.delete(`/companies/${companyId}/blacklist`);
          message.success('已成功移出黑名单');
          fetchCompanies();
        } catch (error) {
          message.error(error.response?.data?.detail || '操作失败，请稍后再试');
        }
      }
    });
  };

  // 审核通过
  const handleVerifyCompany = async (companyId, companyName) => {
    Modal.confirm({
      title: '确认审核通过',
      icon: <CheckCircleOutlined />,
      content: (
        <div>
          <p>确定要审核通过 <strong>{companyName}</strong> 吗？</p>
          <p>审核通过后，该供应商将可以正常提交报价。</p>
        </div>
      ),
      okText: '确认通过',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.post(`/companies/${companyId}/verify`);
          message.success('审核通过成功');
          fetchCompanies();
        } catch (error) {
          message.error(error.response?.data?.detail || '操作失败，请稍后再试');
        }
      }
    });
  };

  // 审核拒绝
  const handleRejectCompany = async (companyId, companyName) => {
    Modal.confirm({
      title: '确认审核拒绝',
      icon: <CloseCircleOutlined />,
      content: (
        <div>
          <p>确定要拒绝审核 <strong>{companyName}</strong> 吗？</p>
          <p>拒绝后，该供应商将无法提交报价，直到重新审核通过。</p>
        </div>
      ),
      okText: '确认拒绝',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await api.delete(`/companies/${companyId}/verification/reject`);
          message.success('审核拒绝成功');
          fetchCompanies();
        } catch (error) {
          message.error(error.response?.data?.detail || '操作失败，请稍后再试');
        }
      }
    });
  };

  // 删除供应商
  const handleDeleteCompany = async (companyId, companyName) => {
    Modal.confirm({
      title: '确认删除供应商',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>确定要永久删除供应商 <strong>{companyName}</strong> 吗？</p>
          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>⚠️ 警告：此操作不可撤销！</p>
          <p>删除后将会：</p>
          <ul style={{ marginLeft: '20px', color: '#666' }}>
            <li>永久删除该供应商的所有信息</li>
            <li>删除该供应商关联的用户账户</li>
            <li>删除该供应商的所有历史报价记录</li>
            <li>此操作无法恢复</li>
          </ul>
        </div>
      ),
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      width: 500,
      onOk: async () => {
        try {
          await api.delete(`/companies/${companyId}`);
          message.success('供应商删除成功');
          fetchCompanies();
        } catch (error) {
          message.error(error.response?.data?.detail || '删除失败，请稍后再试');
        }
      }
    });
  };

  // 获取公司状态标签
  const getStatusTags = (company) => {
    const tags = [];
    
    if (company.is_blacklisted) {
      tags.push(<Tag color="red" key="blacklist">黑名单</Tag>);
    }
    
    if (!company.is_verified) {
      tags.push(<Tag color="orange" key="pending">待审核</Tag>);
    } else if (!company.is_blacklisted) {
      tags.push(<Tag color="green" key="verified">已审核</Tag>);
    }
    
    if (company.is_supplier) {
      tags.push(<Tag color="blue" key="supplier">供应商</Tag>);
    }
    
    return tags;
  };

  // 检查是否有管理权限
  const hasManagePermission = currentUser && currentUser.level >= 4;

  // 表格列定义
  const columns = [
    {
      title: '公司名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Space>
          <ShopOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '联系人',
      dataIndex: 'contact_person',
      key: 'contact_person',
      render: (text) => text || '-',
    },
    {
      title: '联系电话',
      dataIndex: 'contact_phone',
      key: 'contact_phone',
      render: (text) => text || '-',
    },
    {
      title: '联系邮箱',
      dataIndex: 'contact_email',
      key: 'contact_email',
      render: (text) => text || '-',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (text) => text || '-',
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Space>
          {getStatusTags(record)}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        if (!hasManagePermission) {
          return <span style={{ color: '#999' }}>无权限</span>;
        }

        return (
          <Space size="small">
            {/* 黑名单操作 */}
            {record.is_blacklisted ? (
              <Button 
                type="primary"
                size="small"
                icon={<UserAddOutlined />}
                onClick={() => handleRemoveFromBlacklist(record.id, record.name)}
              >
                移出黑名单
              </Button>
            ) : (
              <Button 
                danger
                size="small"
                icon={<UserDeleteOutlined />}
                onClick={() => handleAddToBlacklist(record.id, record.name)}
              >
                加入黑名单
              </Button>
            )}

            {/* 审核操作 */}
            {!record.is_verified ? (
              <Button 
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleVerifyCompany(record.id, record.name)}
              >
                审核通过
              </Button>
            ) : (
              <Button 
                danger
                size="small"
                icon={<CloseCircleOutlined />}
                onClick={() => handleRejectCompany(record.id, record.name)}
              >
                审核拒绝
              </Button>
            )}

            {/* 删除操作 */}
            <Button 
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteCompany(record.id, record.name)}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <Content className="p-6">
      <div className="flex justify-between items-center mb-4">
        <Title level={2}>供应商管理</Title>
        {!hasManagePermission && (
          <Tag color="orange">仅总部管理员可操作黑名单和审核</Tag>
        )}
      </div>

      <Card>
        <div className="mb-4 flex gap-4">
          <Input.Search
            placeholder="搜索公司名称、联系人或邮箱"
            allowClear
            enterButton={<SearchOutlined />}
            onSearch={value => setSearchText(value)}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 300 }}
          />
          
          <Select
            placeholder="筛选状态"
            style={{ width: 150 }}
            value={statusFilter}
            onChange={setStatusFilter}
          >
            <Option value="all">全部状态</Option>
            <Option value="normal">正常</Option>
            <Option value="blacklisted">黑名单</Option>
            <Option value="pending">待审核</Option>
          </Select>
        </div>

        <Table
          columns={columns}
          dataSource={filteredCompanies}
          rowKey="id"
          loading={loading}
          pagination={{ 
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </Content>
  );
};

export default CompaniesPage; 