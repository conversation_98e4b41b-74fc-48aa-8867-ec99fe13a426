import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Tabs, Alert, Steps, message } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, InfoCircleOutlined, ShopOutlined, PhoneOutlined, HomeOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { login, register, registerSupplier } from '../store/thunks/authThunks';
import api from '../services/api';

const { Title } = Typography;

const LoginPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { loading, error, isAuthenticated } = useSelector((state) => state.auth);

  // 注册配置状态
  const [registrationConfig, setRegistrationConfig] = useState({
    user_need_verify: false,
    supplier_need_verify: false,
    message: "注册后可立即使用",
    loading: true,
    error: null
  });

  // 供应商注册表单状态
  const [supplierForm] = Form.useForm();
  const [supplierRegistrationStep, setSupplierRegistrationStep] = useState(0);
  const [supplierFormData, setSupplierFormData] = useState({}); // 添加状态保存表单数据

  // 获取注册配置
  const fetchRegistrationConfig = async () => {
    try {
      setRegistrationConfig(prev => ({ ...prev, loading: true, error: null }));
      const response = await api.get('/system-config/public/registration-config');
      setRegistrationConfig({
        user_need_verify: response.user_need_verify,
        supplier_need_verify: response.supplier_need_verify,
        message: response.message,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('获取注册配置失败:', error);
      // 降级处理：使用默认配置
      setRegistrationConfig({
        user_need_verify: false,
        supplier_need_verify: false,
        message: "注册后可立即使用",
        loading: false,
        error: "配置获取失败，使用默认设置"
      });
    }
  };

  // 如果已登录，重定向到首页或来源页面
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // 组件加载时获取注册配置
  useEffect(() => {
    fetchRegistrationConfig();
  }, []);

  // 处理登录表单提交
  const handleLogin = (values) => {
    dispatch(login({
      username: values.username,
      password: values.password
    }));
  };

  // 处理注册表单提交
  const handleRegister = (values) => {
    dispatch(register({
      username: values.username,
      email: values.email,
      password: values.password,
      name: values.name,
      level: 1, // 默认为普通用户
      is_active: true
    })).unwrap()
      .then(() => {
        // 根据配置显示不同的成功提示
        const successMessage = registrationConfig.user_need_verify
          ? "注册成功！请等待管理员审核后使用。"
          : "注册成功！正在为您登录...";

        message.success(successMessage);

        // 如果不需要审核，自动登录
        if (!registrationConfig.user_need_verify) {
          dispatch(login({
            username: values.username,
            password: values.password
          }));
        }
      })
      .catch((error) => {
        message.error(error || '注册失败，请稍后再试');
      });
  };

  // 处理供应商注册表单提交
  const handleSupplierRegister = (values) => {
    // 手动获取所有表单字段的值
    const allFormValues = supplierForm.getFieldsValue();
    console.log('供应商注册表单数据 (onFinish values):', values); // 添加调试日志
    console.log('供应商注册表单数据 (getFieldsValue):', allFormValues); // 添加调试日志
    console.log('供应商注册表单数据 (状态保存):', supplierFormData); // 添加调试日志
    
    // 使用状态保存的数据，合并当前步骤的数据
    const formData = {
      ...supplierFormData,
      ...values,
      ...allFormValues
    };
    console.log('最终使用的表单数据:', formData); // 添加调试日志
    
    // 确保所有必需字段都存在
    if (!formData.username || !formData.email || !formData.password || !formData.company_name) {
      message.error('请完善所有必需的注册信息');
      console.error('缺少必需字段:', {
        username: !!formData.username,
        email: !!formData.email,
        password: !!formData.password,
        company_name: !!formData.company_name
      });
      return;
    }

    dispatch(registerSupplier({
      username: formData.username,
      email: formData.email,
      password: formData.password,
      name: formData.name,
      phone: formData.phone,
      company_name: formData.company_name,
      company_address: formData.company_address,
      contact_person: formData.contact_person || formData.name,
      contact_phone: formData.contact_phone || formData.phone,
      contact_email: formData.contact_email || formData.email
    })).unwrap()
      .then((response) => {
        message.success(response.next_steps?.message || '供应商注册成功！');

        // 如果可以登录，自动登录
        if (response.next_steps?.can_login) {
          dispatch(login({
            username: formData.username,
            password: formData.password
          }));
        }

        // 重置表单和步骤
        supplierForm.resetFields();
        setSupplierRegistrationStep(0);
        setSupplierFormData({}); // 重置状态数据
      })
      .catch((error) => {
        console.error('供应商注册错误:', error); // 添加调试日志
        message.error(error || '供应商注册失败，请稍后再试');
      });
  };

  // 登录表单组件
  const LoginForm = () => (
    <Form
      name="login"
      initialValues={{ remember: true }}
      onFinish={handleLogin}
      size="large"
    >
      <Form.Item
        name="username"
        rules={[{ required: true, message: '请输入用户名!' }]}
      >
        <Input prefix={<UserOutlined />} placeholder="用户名" />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[{ required: true, message: '请输入密码!' }]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="密码" />
      </Form.Item>

      <Form.Item>
        <Form.Item name="remember" valuePropName="checked" noStyle>
          <Checkbox>记住我</Checkbox>
        </Form.Item>

        <a className="float-right" href="#">忘记密码</a>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="w-full"
          loading={loading}
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );

  // 注册表单组件
  const RegisterForm = () => (
    <Form
      name="register"
      onFinish={handleRegister}
      size="large"
    >
      {/* 注册审核提示 */}
      {!registrationConfig.loading && (
        <Alert
          message="注册提示"
          description={registrationConfig.message}
          type={registrationConfig.user_need_verify ? "info" : "success"}
          icon={<InfoCircleOutlined />}
          showIcon
          className="mb-4"
        />
      )}

      {/* 配置获取错误提示 */}
      {registrationConfig.error && (
        <Alert
          message="提示"
          description={registrationConfig.error}
          type="warning"
          showIcon
          className="mb-4"
        />
      )}
      <Form.Item
        name="username"
        rules={[{ required: true, message: '请输入用户名!' }]}
      >
        <Input prefix={<UserOutlined />} placeholder="用户名" />
      </Form.Item>

      <Form.Item
        name="name"
        rules={[{ required: true, message: '请输入姓名!' }]}
      >
        <Input prefix={<UserOutlined />} placeholder="姓名" />
      </Form.Item>

      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱!' },
          { type: 'email', message: '请输入有效的邮箱地址!' }
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="邮箱" />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码!' },
          { min: 6, message: '密码长度不能少于6个字符!' }
        ]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="密码" />
      </Form.Item>

      <Form.Item
        name="confirm"
        dependencies={['password']}
        rules={[
          { required: true, message: '请确认密码!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('两次输入的密码不一致!'));
            },
          }),
        ]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="确认密码" />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="w-full"
          loading={loading || registrationConfig.loading}
        >
          {registrationConfig.loading ? "加载中..." : "注册"}
        </Button>
      </Form.Item>
    </Form>
  );

  // 供应商注册表单组件
  const SupplierRegisterForm = () => {
    const steps = [
      {
        title: '用户信息',
        icon: <UserOutlined />
      },
      {
        title: '公司信息',
        icon: <ShopOutlined />
      },
      {
        title: '完成注册',
        icon: <InfoCircleOutlined />
      }
    ];

    const nextStep = () => {
      // 根据当前步骤验证相应字段
      let fieldsToValidate = [];
      if (supplierRegistrationStep === 0) {
        fieldsToValidate = ['username', 'name', 'email', 'phone', 'password', 'confirm'];
      } else if (supplierRegistrationStep === 1) {
        fieldsToValidate = ['company_name'];
      }

      supplierForm.validateFields(fieldsToValidate).then((values) => {
        console.log('当前步骤验证通过，数据:', values); // 添加调试日志
        
        // 保存当前步骤的数据到状态中
        setSupplierFormData(prevData => ({
          ...prevData,
          ...values
        }));
        
        setSupplierRegistrationStep(supplierRegistrationStep + 1);
      }).catch((errorInfo) => {
        console.error('表单验证失败:', errorInfo); // 添加调试日志
        message.error('请完善当前步骤的信息');
      });
    };

    const prevStep = () => {
      setSupplierRegistrationStep(supplierRegistrationStep - 1);
    };

    return (
      <div>
        {/* 注册审核提示 */}
        {!registrationConfig.loading && (
          <Alert
            message="供应商注册提示"
            description={registrationConfig.message}
            type={(registrationConfig.user_need_verify || registrationConfig.supplier_need_verify) ? "info" : "success"}
            icon={<InfoCircleOutlined />}
            showIcon
            className="mb-4"
          />
        )}

        {/* 配置获取错误提示 */}
        {registrationConfig.error && (
          <Alert
            message="提示"
            description={registrationConfig.error}
            type="warning"
            showIcon
            className="mb-4"
          />
        )}

        {/* 步骤指示器 */}
        <Steps current={supplierRegistrationStep} items={steps} className="mb-6" />

        <Form
          form={supplierForm}
          name="supplier-register"
          onFinish={handleSupplierRegister}
          size="large"
          layout="vertical"
          preserve={true}
          scrollToFirstError
        >
          {/* 第一步：用户信息 */}
          {supplierRegistrationStep === 0 && (
            <div>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名!' }]}
              >
                <Input prefix={<UserOutlined />} placeholder="用户名" />
              </Form.Item>

              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名!' }]}
              >
                <Input prefix={<UserOutlined />} placeholder="姓名" />
              </Form.Item>

              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱!' },
                  { type: 'email', message: '请输入有效的邮箱地址!' }
                ]}
              >
                <Input prefix={<MailOutlined />} placeholder="邮箱" />
              </Form.Item>

              <Form.Item
                name="phone"
                label="手机号码"
                rules={[{ required: true, message: '请输入手机号码!' }]}
              >
                <Input prefix={<PhoneOutlined />} placeholder="手机号码" />
              </Form.Item>

              <Form.Item
                name="password"
                label="密码"
                rules={[
                  { required: true, message: '请输入密码!' },
                  { min: 6, message: '密码长度不能少于6个字符!' }
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="密码" />
              </Form.Item>

              <Form.Item
                name="confirm"
                label="确认密码"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码!' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致!'));
                    },
                  }),
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="确认密码" />
              </Form.Item>

              <Form.Item>
                <Button type="primary" onClick={nextStep} className="w-full">
                  下一步
                </Button>
              </Form.Item>
            </div>
          )}

          {/* 第二步：公司信息 */}
          {supplierRegistrationStep === 1 && (
            <div>
              <Form.Item
                name="company_name"
                label="公司名称"
                rules={[{ required: true, message: '请输入公司名称!' }]}
              >
                <Input prefix={<ShopOutlined />} placeholder="公司名称" />
              </Form.Item>

              <Form.Item
                name="company_address"
                label="公司地址"
              >
                <Input prefix={<HomeOutlined />} placeholder="公司地址" />
              </Form.Item>

              <Form.Item
                name="contact_person"
                label="联系人"
                tooltip="如不填写，将使用用户姓名"
              >
                <Input prefix={<UserOutlined />} placeholder="联系人（可选）" />
              </Form.Item>

              <Form.Item
                name="contact_phone"
                label="联系电话"
                tooltip="如不填写，将使用用户手机号"
              >
                <Input prefix={<PhoneOutlined />} placeholder="联系电话（可选）" />
              </Form.Item>

              <Form.Item
                name="contact_email"
                label="联系邮箱"
                tooltip="如不填写，将使用用户邮箱"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址!' }
                ]}
              >
                <Input prefix={<MailOutlined />} placeholder="联系邮箱（可选）" />
              </Form.Item>

              <Form.Item>
                <div className="flex gap-2">
                  <Button onClick={prevStep} className="flex-1">
                    上一步
                  </Button>
                  <Button type="primary" onClick={nextStep} className="flex-1">
                    下一步
                  </Button>
                </div>
              </Form.Item>
            </div>
          )}

          {/* 第三步：确认注册 */}
          {supplierRegistrationStep === 2 && (
            <div>
              <Alert
                message="请确认注册信息"
                description="请仔细核对以下信息，确认无误后点击注册按钮完成注册。"
                type="info"
                showIcon
                className="mb-4"
              />

              <div className="bg-gray-50 p-4 rounded mb-4">
                <h4 className="font-medium mb-2">用户信息</h4>
                <p>用户名: {supplierForm.getFieldValue('username')}</p>
                <p>姓名: {supplierForm.getFieldValue('name')}</p>
                <p>邮箱: {supplierForm.getFieldValue('email')}</p>
                <p>手机: {supplierForm.getFieldValue('phone')}</p>
              </div>

              <div className="bg-gray-50 p-4 rounded mb-4">
                <h4 className="font-medium mb-2">公司信息</h4>
                <p>公司名称: {supplierForm.getFieldValue('company_name')}</p>
                <p>公司地址: {supplierForm.getFieldValue('company_address') || '未填写'}</p>
                <p>联系人: {supplierForm.getFieldValue('contact_person') || supplierForm.getFieldValue('name')}</p>
                <p>联系电话: {supplierForm.getFieldValue('contact_phone') || supplierForm.getFieldValue('phone')}</p>
                <p>联系邮箱: {supplierForm.getFieldValue('contact_email') || supplierForm.getFieldValue('email')}</p>
              </div>

              <Form.Item>
                <div className="flex gap-2">
                  <Button onClick={prevStep} className="flex-1">
                    上一步
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    className="flex-1"
                    loading={loading || registrationConfig.loading}
                  >
                    {registrationConfig.loading ? "加载中..." : "注册"}
                  </Button>
                </div>
              </Form.Item>
            </div>
          )}
        </Form>
      </div>
    );
  };

  const tabItems = [
    {
      key: 'login',
      label: '登录',
      children: <LoginForm />
    },
    {
      key: 'register',
      label: '用户注册',
      children: <RegisterForm />
    },
    {
      key: 'supplier-register',
      label: '供应商注册',
      children: <SupplierRegisterForm />
    }
  ];

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-2xl">
        <div className="text-center mb-6">
          <Title level={3}>供应商询价平台</Title>
        </div>

        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            className="mb-4"
            closable
          />
        )}

        <Tabs defaultActiveKey="login" items={tabItems} />

        <div className="text-center mt-4">
          <Link to="/">返回首页</Link>
        </div>
      </Card>
    </div>
  );
};

export default LoginPage;
