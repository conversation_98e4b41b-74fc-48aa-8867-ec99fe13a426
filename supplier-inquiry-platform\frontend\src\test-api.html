<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>API测试页面</h1>
    
    <div class="test-section">
        <h3>测试1: 系统配置API</h3>
        <button onclick="testSystemConfig()">测试系统配置</button>
        <div id="test1-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试2: 供应商注册API (422错误)</h3>
        <button onclick="testSupplierRegister()">测试供应商注册</button>
        <div id="test2-result" class="result"></div>
    </div>

    <script>
        // 创建axios实例
        const api = axios.create({
            baseURL: 'http://localhost:5000/api',
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
            }
        });

        // 测试系统配置API
        async function testSystemConfig() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await api.get('/system-config/public/registration-config');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `成功获取系统配置:\n${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `获取系统配置失败:\n${JSON.stringify({
                    message: error.message,
                    status: error.response?.status,
                    data: error.response?.data
                }, null, 2)}`;
            }
        }

        // 测试供应商注册API (故意发送无效数据)
        async function testSupplierRegister() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await api.post('/auth/register-supplier', {
                    username: '',
                    email: 'invalid-email',
                    password: '',
                    company_name: ''
                });
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `意外成功:\n${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `预期的422错误:\n${JSON.stringify({
                    message: error.message,
                    status: error.response?.status,
                    data: error.response?.data
                }, null, 2)}`;
            }
        }
    </script>
    
    <!-- 引入axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</body>
</html>
